import React, { useState, useEffect } from 'react'
import { Search, Trophy } from 'lucide-react'
import { toast } from 'react-hot-toast'
import Table from '../components/shared/Table'
import { SelectField } from '../components/shared/Form'
import { standingsAPI, leaguesAPI } from '../services/supabase'

const Standings = () => {
  const [standings, setStandings] = useState([])
  const [leagues, setLeagues] = useState([])
  const [selectedLeague, setSelectedLeague] = useState('')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadLeagues()
  }, [])

  useEffect(() => {
    if (selectedLeague) {
      loadStandings()
    }
  }, [selectedLeague])

  const loadLeagues = async () => {
    try {
      const data = await leaguesAPI.getAll()
      setLeagues(data)
      if (data.length > 0) {
        setSelectedLeague(data[0].id)
      }
    } catch (error) {
      console.error('Error loading leagues:', error)
      toast.error('حدث خطأ في تحميل البطولات')
    }
  }

  const loadStandings = async () => {
    try {
      setLoading(true)
      const data = await standingsAPI.getByLeague(selectedLeague)
      setStandings(data)
    } catch (error) {
      console.error('Error loading standings:', error)
      toast.error('حدث خطأ في تحميل جدول الترتيب')
    } finally {
      setLoading(false)
    }
  }

  const handleLeagueChange = (e) => {
    setSelectedLeague(e.target.value)
  }

  const columns = [
    {
      header: 'المركز',
      key: 'position',
      render: (value, row, index) => (
        <div className="flex items-center">
          <span className="font-bold text-lg">{index + 1}</span>
          {index < 3 && (
            <Trophy className={`h-4 w-4 mr-1 ${
              index === 0 ? 'text-yellow-500' : 
              index === 1 ? 'text-gray-400' : 
              'text-orange-600'
            }`} />
          )}
        </div>
      )
    },
    {
      header: 'الفريق',
      key: 'team',
      render: (value, row) => (
        <div className="flex items-center space-x-2 space-x-reverse">
          {row.teams?.logo_url && (
            <img src={row.teams.logo_url} alt="" className="w-8 h-8 rounded" />
          )}
          <span className="font-medium">{row.teams?.name}</span>
        </div>
      )
    },
    {
      header: 'لعب',
      key: 'played'
    },
    {
      header: 'فوز',
      key: 'won'
    },
    {
      header: 'تعادل',
      key: 'drawn'
    },
    {
      header: 'خسارة',
      key: 'lost'
    },
    {
      header: 'الأهداف المسجلة',
      key: 'goals_for'
    },
    {
      header: 'الأهداف المستقبلة',
      key: 'goals_against'
    },
    {
      header: 'فارق الأهداف',
      key: 'goal_difference',
      render: (value) => (
        <span className={`font-medium ${
          value > 0 ? 'text-green-600' : 
          value < 0 ? 'text-red-600' : 
          'text-secondary-600'
        }`}>
          {value > 0 ? '+' : ''}{value}
        </span>
      )
    },
    {
      header: 'النقاط',
      key: 'points',
      render: (value) => (
        <span className="font-bold text-lg text-primary-600">{value}</span>
      )
    }
  ]

  const leagueOptions = leagues.map(league => ({
    value: league.id,
    label: league.name
  }))

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-secondary-900">جدول الترتيب</h1>
      </div>

      {/* League Selection */}
      <div className="card">
        <div className="max-w-md">
          <SelectField
            label="اختر البطولة"
            name="league"
            value={selectedLeague}
            onChange={handleLeagueChange}
            options={leagueOptions}
            placeholder="اختر البطولة"
          />
        </div>
      </div>

      {/* Standings Table */}
      {selectedLeague && (
        <div className="card overflow-hidden p-0">
          <div className="bg-primary-50 px-6 py-4 border-b border-primary-200">
            <h2 className="text-lg font-semibold text-primary-900 flex items-center">
              <Trophy className="h-5 w-5 ml-2" />
              {leagues.find(l => l.id === selectedLeague)?.name} - جدول الترتيب
            </h2>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-secondary-200">
              <thead className="table-header">
                <tr>
                  {columns.map((column, index) => (
                    <th
                      key={index}
                      className="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider"
                    >
                      {column.header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-secondary-200">
                {loading ? (
                  <tr>
                    <td colSpan={columns.length} className="px-6 py-12 text-center">
                      <div className="animate-pulse">
                        <div className="h-4 bg-secondary-200 rounded w-1/4 mx-auto mb-4"></div>
                        <div className="space-y-3">
                          {[...Array(5)].map((_, i) => (
                            <div key={i} className="h-4 bg-secondary-200 rounded"></div>
                          ))}
                        </div>
                      </div>
                    </td>
                  </tr>
                ) : standings.length > 0 ? (
                  standings.map((row, rowIndex) => (
                    <tr key={row.id} className={`
                      hover:bg-secondary-50 transition-colors
                      ${rowIndex < 4 ? 'bg-green-50' : ''}
                      ${rowIndex >= standings.length - 3 ? 'bg-red-50' : ''}
                    `}>
                      {columns.map((column, colIndex) => (
                        <td key={colIndex} className="table-cell">
                          {column.render ? column.render(row[column.key], row, rowIndex) : row[column.key]}
                        </td>
                      ))}
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={columns.length} className="px-6 py-12 text-center text-secondary-500">
                      لا توجد بيانات ترتيب لهذه البطولة
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          
          {/* Legend */}
          {standings.length > 0 && (
            <div className="px-6 py-4 bg-secondary-50 border-t border-secondary-200">
              <div className="flex flex-wrap gap-4 text-xs">
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-green-100 rounded mr-2"></div>
                  <span>مراكز التأهل</span>
                </div>
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-red-100 rounded mr-2"></div>
                  <span>مراكز الهبوط</span>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default Standings
