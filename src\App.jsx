import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Toaster } from 'react-hot-toast'
import Layout from './components/Layout'
import Dashboard from './pages/Dashboard'
import Matches from './pages/Matches'
import Teams from './pages/Teams'
import Players from './pages/Players'
import Leagues from './pages/Leagues'
import News from './pages/News'
import Standings from './pages/Standings'
import MatchStats from './pages/MatchStats'

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-secondary-50">
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/matches" element={<Matches />} />
            <Route path="/teams" element={<Teams />} />
            <Route path="/players" element={<Players />} />
            <Route path="/leagues" element={<Leagues />} />
            <Route path="/news" element={<News />} />
            <Route path="/standings" element={<Standings />} />
            <Route path="/match-stats" element={<MatchStats />} />
          </Routes>
        </Layout>
        <Toaster 
          position="top-left"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
              fontFamily: 'Cairo, sans-serif',
              direction: 'rtl'
            },
          }}
        />
      </div>
    </Router>
  )
}

export default App
