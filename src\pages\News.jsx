import React, { useState, useEffect } from 'react'
import { Plus, Search } from 'lucide-react'
import { toast } from 'react-hot-toast'
import Table from '../components/shared/Table'
import Modal from '../components/shared/Modal'
import { InputField, TextareaField, FormButtons } from '../components/shared/Form'
import { newsAPI } from '../services/supabase'
import { format } from 'date-fns'

const News = () => {
  const [news, setNews] = useState([])
  const [loading, setLoading] = useState(true)
  const [modalOpen, setModalOpen] = useState(false)
  const [editingNews, setEditingNews] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    image_url: '',
    author: '',
    published: false
  })
  const [formLoading, setFormLoading] = useState(false)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const data = await newsAPI.getAll()
      setNews(data)
    } catch (error) {
      console.error('Error loading news:', error)
      toast.error('حدث خطأ في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    try {
      setFormLoading(true)
      
      if (editingNews) {
        await newsAPI.update(editingNews.id, formData)
        toast.success('تم تحديث الخبر بنجاح')
      } else {
        await newsAPI.create(formData)
        toast.success('تم إضافة الخبر بنجاح')
      }
      
      await loadData()
      closeModal()
    } catch (error) {
      console.error('Error saving news:', error)
      toast.error('حدث خطأ في حفظ الخبر')
    } finally {
      setFormLoading(false)
    }
  }

  const handleEdit = (newsItem) => {
    setEditingNews(newsItem)
    setFormData({
      title: newsItem.title,
      content: newsItem.content || '',
      image_url: newsItem.image_url || '',
      author: newsItem.author || '',
      published: newsItem.published || false
    })
    setModalOpen(true)
  }

  const handleDelete = async (newsItem) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الخبر؟')) {
      try {
        await newsAPI.delete(newsItem.id)
        toast.success('تم حذف الخبر بنجاح')
        await loadData()
      } catch (error) {
        console.error('Error deleting news:', error)
        toast.error('حدث خطأ في حذف الخبر')
      }
    }
  }

  const openAddModal = () => {
    setEditingNews(null)
    setFormData({
      title: '',
      content: '',
      image_url: '',
      author: '',
      published: false
    })
    setModalOpen(true)
  }

  const closeModal = () => {
    setModalOpen(false)
    setEditingNews(null)
  }

  const filteredNews = news.filter(item =>
    item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.author?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const columns = [
    {
      header: 'الصورة',
      key: 'image_url',
      render: (value) => (
        value ? (
          <img src={value} alt="صورة الخبر" className="w-16 h-12 rounded object-cover" />
        ) : (
          <div className="w-16 h-12 bg-secondary-200 rounded flex items-center justify-center">
            <span className="text-secondary-500 text-xs">لا يوجد</span>
          </div>
        )
      )
    },
    {
      header: 'العنوان',
      key: 'title',
      render: (value) => (
        <div className="max-w-xs">
          <p className="truncate font-medium">{value}</p>
        </div>
      )
    },
    {
      header: 'الكاتب',
      key: 'author'
    },
    {
      header: 'الحالة',
      key: 'published',
      render: (value) => (
        <span className={`
          px-2 py-1 rounded-full text-xs font-medium
          ${value 
            ? 'text-green-600 bg-green-100' 
            : 'text-orange-600 bg-orange-100'
          }
        `}>
          {value ? 'منشور' : 'مسودة'}
        </span>
      )
    },
    {
      header: 'تاريخ الإنشاء',
      key: 'created_at',
      render: (value) => format(new Date(value), 'dd/MM/yyyy HH:mm')
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-secondary-900">إدارة الأخبار</h1>
        <button
          onClick={openAddModal}
          className="btn-primary flex items-center"
        >
          <Plus className="h-5 w-5 ml-2" />
          إضافة خبر
        </button>
      </div>

      {/* Search */}
      <div className="card">
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-400 h-5 w-5" />
          <input
            type="text"
            placeholder="البحث في الأخبار..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="input-field pr-10"
          />
        </div>
      </div>

      {/* News Table */}
      <Table
        columns={columns}
        data={filteredNews}
        onEdit={handleEdit}
        onDelete={handleDelete}
        loading={loading}
        emptyMessage="لا توجد أخبار"
      />

      {/* Add/Edit Modal */}
      <Modal
        isOpen={modalOpen}
        onClose={closeModal}
        title={editingNews ? 'تعديل الخبر' : 'إضافة خبر جديد'}
        size="lg"
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <InputField
            label="عنوان الخبر"
            name="title"
            value={formData.title}
            onChange={handleInputChange}
            placeholder="أدخل عنوان الخبر"
            required
          />

          <TextareaField
            label="محتوى الخبر"
            name="content"
            value={formData.content}
            onChange={handleInputChange}
            placeholder="أدخل محتوى الخبر..."
            rows={6}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <InputField
              label="الكاتب"
              name="author"
              value={formData.author}
              onChange={handleInputChange}
              placeholder="اسم الكاتب"
            />
            
            <div className="flex items-center mt-8">
              <input
                type="checkbox"
                id="published"
                name="published"
                checked={formData.published}
                onChange={handleInputChange}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded"
              />
              <label htmlFor="published" className="mr-2 text-sm text-secondary-700">
                نشر الخبر
              </label>
            </div>
          </div>

          <InputField
            label="رابط الصورة"
            name="image_url"
            type="url"
            value={formData.image_url}
            onChange={handleInputChange}
            placeholder="https://example.com/image.jpg"
          />

          <FormButtons
            onSubmit={handleSubmit}
            onCancel={closeModal}
            loading={formLoading}
            submitText={editingNews ? 'تحديث' : 'إضافة'}
          />
        </form>
      </Modal>
    </div>
  )
}

export default News
