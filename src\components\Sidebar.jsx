import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { 
  LayoutDashboard, 
  Calendar, 
  Users, 
  UserCheck, 
  Trophy, 
  Newspaper, 
  BarChart3, 
  TrendingUp,
  X
} from 'lucide-react'

const Sidebar = ({ isOpen, onClose }) => {
  const location = useLocation()

  const menuItems = [
    {
      name: 'الرئيسية',
      href: '/',
      icon: LayoutDashboard
    },
    {
      name: 'المباريات',
      href: '/matches',
      icon: Calendar
    },
    {
      name: 'الفرق',
      href: '/teams',
      icon: Users
    },
    {
      name: 'اللاعبين',
      href: '/players',
      icon: UserCheck
    },
    {
      name: 'البطولات',
      href: '/leagues',
      icon: Trophy
    },
    {
      name: 'الأخبار',
      href: '/news',
      icon: Newspaper
    },
    {
      name: 'جدول الترتيب',
      href: '/standings',
      icon: BarChart3
    },
    {
      name: 'إحصائيات المباريات',
      href: '/match-stats',
      icon: TrendingUp
    }
  ]

  return (
    <>
      {/* Desktop Sidebar */}
      <div className="hidden lg:flex lg:flex-shrink-0">
        <div className="flex flex-col w-64">
          <div className="flex flex-col flex-grow bg-white border-l border-secondary-200 pt-5 pb-4 overflow-y-auto">
            {/* Logo */}
            <div className="flex items-center flex-shrink-0 px-4 mb-8">
              <div className="h-8 w-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <Trophy className="h-5 w-5 text-white" />
              </div>
              <span className="mr-3 text-xl font-bold text-secondary-900">
                الرياضة
              </span>
            </div>

            {/* Navigation */}
            <nav className="mt-5 flex-1 px-2 space-y-1">
              {menuItems.map((item) => {
                const Icon = item.icon
                const isActive = location.pathname === item.href
                
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`
                      group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors
                      ${isActive 
                        ? 'bg-primary-100 text-primary-900 border-l-4 border-primary-600' 
                        : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'
                      }
                    `}
                  >
                    <Icon className={`
                      ml-3 flex-shrink-0 h-6 w-6 transition-colors
                      ${isActive ? 'text-primary-600' : 'text-secondary-400 group-hover:text-secondary-500'}
                    `} />
                    {item.name}
                  </Link>
                )
              })}
            </nav>
          </div>
        </div>
      </div>

      {/* Mobile Sidebar */}
      <div className={`lg:hidden fixed inset-y-0 right-0 z-50 w-64 bg-white transform transition-transform duration-300 ease-in-out ${
        isOpen ? 'translate-x-0' : 'translate-x-full'
      }`}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-secondary-200">
            <div className="flex items-center">
              <div className="h-8 w-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <Trophy className="h-5 w-5 text-white" />
              </div>
              <span className="mr-3 text-xl font-bold text-secondary-900">
                الرياضة
              </span>
            </div>
            <button
              onClick={onClose}
              className="p-2 rounded-md text-secondary-600 hover:bg-secondary-100"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-2 py-4 space-y-1">
            {menuItems.map((item) => {
              const Icon = item.icon
              const isActive = location.pathname === item.href
              
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={onClose}
                  className={`
                    group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors
                    ${isActive 
                      ? 'bg-primary-100 text-primary-900 border-l-4 border-primary-600' 
                      : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'
                    }
                  `}
                >
                  <Icon className={`
                    ml-3 flex-shrink-0 h-6 w-6 transition-colors
                    ${isActive ? 'text-primary-600' : 'text-secondary-400 group-hover:text-secondary-500'}
                  `} />
                  {item.name}
                </Link>
              )
            })}
          </nav>
        </div>
      </div>
    </>
  )
}

export default Sidebar
