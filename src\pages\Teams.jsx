import React, { useState, useEffect } from 'react'
import { Plus, Search } from 'lucide-react'
import { toast } from 'react-hot-toast'
import Table from '../components/shared/Table'
import Modal from '../components/shared/Modal'
import { InputField, SelectField, FormButtons } from '../components/shared/Form'
import { teamsAPI, leaguesAPI } from '../services/supabase'

const Teams = () => {
  const [teams, setTeams] = useState([])
  const [leagues, setLeagues] = useState([])
  const [loading, setLoading] = useState(true)
  const [modalOpen, setModalOpen] = useState(false)
  const [editingTeam, setEditingTeam] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [formData, setFormData] = useState({
    name: '',
    country: '',
    logo_url: '',
    league_id: '',
    founded_year: '',
    stadium: ''
  })
  const [formLoading, setFormLoading] = useState(false)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const [teamsData, leaguesData] = await Promise.all([
        teamsAPI.getAll(),
        leaguesAPI.getAll()
      ])
      
      setTeams(teamsData)
      setLeagues(leaguesData)
    } catch (error) {
      console.error('Error loading data:', error)
      toast.error('حدث خطأ في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    try {
      setFormLoading(true)
      
      if (editingTeam) {
        await teamsAPI.update(editingTeam.id, formData)
        toast.success('تم تحديث الفريق بنجاح')
      } else {
        await teamsAPI.create(formData)
        toast.success('تم إضافة الفريق بنجاح')
      }
      
      await loadData()
      closeModal()
    } catch (error) {
      console.error('Error saving team:', error)
      toast.error('حدث خطأ في حفظ الفريق')
    } finally {
      setFormLoading(false)
    }
  }

  const handleEdit = (team) => {
    setEditingTeam(team)
    setFormData({
      name: team.name,
      country: team.country || '',
      logo_url: team.logo_url || '',
      league_id: team.league_id || '',
      founded_year: team.founded_year || '',
      stadium: team.stadium || ''
    })
    setModalOpen(true)
  }

  const handleDelete = async (team) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الفريق؟')) {
      try {
        await teamsAPI.delete(team.id)
        toast.success('تم حذف الفريق بنجاح')
        await loadData()
      } catch (error) {
        console.error('Error deleting team:', error)
        toast.error('حدث خطأ في حذف الفريق')
      }
    }
  }

  const openAddModal = () => {
    setEditingTeam(null)
    setFormData({
      name: '',
      country: '',
      logo_url: '',
      league_id: '',
      founded_year: '',
      stadium: ''
    })
    setModalOpen(true)
  }

  const closeModal = () => {
    setModalOpen(false)
    setEditingTeam(null)
  }

  const filteredTeams = teams.filter(team =>
    team.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    team.country?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    team.leagues?.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const columns = [
    {
      header: 'الشعار',
      key: 'logo_url',
      render: (value) => (
        value ? (
          <img src={value} alt="شعار الفريق" className="w-10 h-10 rounded-full object-cover" />
        ) : (
          <div className="w-10 h-10 bg-secondary-200 rounded-full flex items-center justify-center">
            <span className="text-secondary-500 text-xs">لا يوجد</span>
          </div>
        )
      )
    },
    {
      header: 'اسم الفريق',
      key: 'name'
    },
    {
      header: 'الدولة',
      key: 'country'
    },
    {
      header: 'البطولة',
      key: 'league',
      render: (value, row) => row.leagues?.name || 'غير محدد'
    },
    {
      header: 'سنة التأسيس',
      key: 'founded_year'
    },
    {
      header: 'الملعب',
      key: 'stadium'
    }
  ]

  const leagueOptions = leagues.map(league => ({
    value: league.id,
    label: league.name
  }))

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-secondary-900">إدارة الفرق</h1>
        <button
          onClick={openAddModal}
          className="btn-primary flex items-center"
        >
          <Plus className="h-5 w-5 ml-2" />
          إضافة فريق
        </button>
      </div>

      {/* Search */}
      <div className="card">
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-400 h-5 w-5" />
          <input
            type="text"
            placeholder="البحث في الفرق..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="input-field pr-10"
          />
        </div>
      </div>

      {/* Teams Table */}
      <Table
        columns={columns}
        data={filteredTeams}
        onEdit={handleEdit}
        onDelete={handleDelete}
        loading={loading}
        emptyMessage="لا توجد فرق"
      />

      {/* Add/Edit Modal */}
      <Modal
        isOpen={modalOpen}
        onClose={closeModal}
        title={editingTeam ? 'تعديل الفريق' : 'إضافة فريق جديد'}
        size="lg"
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <InputField
            label="اسم الفريق"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            placeholder="أدخل اسم الفريق"
            required
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <InputField
              label="الدولة"
              name="country"
              value={formData.country}
              onChange={handleInputChange}
              placeholder="أدخل اسم الدولة"
            />
            
            <InputField
              label="سنة التأسيس"
              name="founded_year"
              type="number"
              min="1800"
              max={new Date().getFullYear()}
              value={formData.founded_year}
              onChange={handleInputChange}
              placeholder="مثال: 1950"
            />
          </div>

          <SelectField
            label="البطولة"
            name="league_id"
            value={formData.league_id}
            onChange={handleInputChange}
            options={leagueOptions}
            placeholder="اختر البطولة"
          />

          <InputField
            label="الملعب"
            name="stadium"
            value={formData.stadium}
            onChange={handleInputChange}
            placeholder="أدخل اسم الملعب"
          />

          <InputField
            label="رابط الشعار"
            name="logo_url"
            type="url"
            value={formData.logo_url}
            onChange={handleInputChange}
            placeholder="https://example.com/logo.png"
          />

          <FormButtons
            onSubmit={handleSubmit}
            onCancel={closeModal}
            loading={formLoading}
            submitText={editingTeam ? 'تحديث' : 'إضافة'}
          />
        </form>
      </Modal>
    </div>
  )
}

export default Teams
