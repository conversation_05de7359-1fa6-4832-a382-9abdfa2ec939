# 🚀 البدء السريع - Quick Start

## خطوات سريعة للتشغيل (5 دقائق)

### 1️⃣ تثبيت التبعيات
```bash
npm install
```

### 2️⃣ إعداد Supabase
1. اذهب إلى [supabase.com](https://supabase.com) وأنشئ مشروع جديد
2. في SQL Editor، شغل محتوى ملف `database-schema.sql`
3. احصل على Project URL و API Key من Settings > API

### 3️⃣ إعداد متغيرات البيئة
```bash
cp .env.example .env
```

عدل ملف `.env`:
```env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
```

### 4️⃣ تشغيل المشروع
```bash
npm run dev
```

🎉 **تم!** افتح http://localhost:3000

---

## 📝 إضافة بيانات تجريبية

### في Supabase SQL Editor:

```sql
-- إضافة بطولة
INSERT INTO leagues (name, country) VALUES 
('الدوري السعودي', 'السعودية');

-- إضافة فرق (استبدل league_id بالـ ID الحقيقي)
INSERT INTO teams (name, country, league_id) VALUES 
('الهلال', 'السعودية', 'your-league-id'),
('النصر', 'السعودية', 'your-league-id');

-- إضافة خبر
INSERT INTO news (title, content, published) VALUES 
('مرحباً بكم في لوحة التحكم', 'هذا خبر تجريبي لاختبار النظام', true);
```

---

## 🔧 حل المشاكل الشائعة

### ❌ خطأ "Missing Supabase environment variables"
- تأكد من وجود ملف `.env` في المجلد الرئيسي
- تأكد من صحة أسماء المتغيرات: `VITE_SUPABASE_URL` و `VITE_SUPABASE_ANON_KEY`

### ❌ خطأ في تحميل البيانات
- تأكد من تشغيل `database-schema.sql` في Supabase
- تحقق من صحة URL و API Key

### ❌ خطأ في Tailwind CSS
- أعد تشغيل الخادم: `npm run dev`
- تأكد من وجود ملف `tailwind.config.js`

---

## 📱 الصفحات المتاحة

| الصفحة | الرابط | الوصف |
|--------|--------|-------|
| الرئيسية | `/` | إحصائيات عامة |
| المباريات | `/matches` | إدارة المباريات |
| الفرق | `/teams` | إدارة الفرق |
| اللاعبين | `/players` | إدارة اللاعبين |
| البطولات | `/leagues` | إدارة البطولات |
| الأخبار | `/news` | إدارة الأخبار |
| الترتيب | `/standings` | جدول الترتيب |
| الإحصائيات | `/match-stats` | إحصائيات المباريات |

---

## 🎯 الخطوات التالية

1. **أضف بيانات حقيقية**: ابدأ بإضافة البطولات والفرق
2. **خصص التصميم**: عدل الألوان في `tailwind.config.js`
3. **أضف الصور**: استخدم روابط صور حقيقية للشعارات
4. **اختبر الوظائف**: جرب إضافة وتعديل وحذف البيانات

---

## 📞 تحتاج مساعدة؟

- راجع ملف `README.md` للتفاصيل الكاملة
- راجع ملف `DEVELOPMENT.md` لدليل التطوير
- أنشئ Issue في GitHub للمشاكل التقنية

---

**نصيحة**: ابدأ بإضافة بطولة واحدة، ثم فريقين، ثم مباراة واحدة لاختبار النظام! 🏆
