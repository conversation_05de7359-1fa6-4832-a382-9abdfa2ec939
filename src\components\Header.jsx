import React from 'react'
import { <PERSON><PERSON>, <PERSON>, User, Setting<PERSON> } from 'lucide-react'

const Header = ({ onMenuClick }) => {
  return (
    <header className="bg-white shadow-sm border-b border-secondary-200">
      <div className="flex items-center justify-between px-6 py-4">
        {/* Right side - Menu button and title */}
        <div className="flex items-center">
          <button
            onClick={onMenuClick}
            className="lg:hidden p-2 rounded-md text-secondary-600 hover:bg-secondary-100"
          >
            <Menu className="h-6 w-6" />
          </button>
          <h1 className="mr-4 text-2xl font-bold text-secondary-900">
            لوحة التحكم الرياضية
          </h1>
        </div>

        {/* Left side - User actions */}
        <div className="flex items-center space-x-4 space-x-reverse">
          {/* Notifications */}
          <button className="p-2 rounded-full text-secondary-600 hover:bg-secondary-100 relative">
            <Bell className="h-6 w-6" />
            <span className="absolute top-0 left-0 h-2 w-2 bg-red-500 rounded-full"></span>
          </button>

          {/* Settings */}
          <button className="p-2 rounded-full text-secondary-600 hover:bg-secondary-100">
            <Settings className="h-6 w-6" />
          </button>

          {/* User Profile */}
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="text-left">
              <p className="text-sm font-medium text-secondary-900">المدير العام</p>
              <p className="text-xs text-secondary-500"><EMAIL></p>
            </div>
            <div className="h-8 w-8 bg-primary-600 rounded-full flex items-center justify-center">
              <User className="h-5 w-5 text-white" />
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
