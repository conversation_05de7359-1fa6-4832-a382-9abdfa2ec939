import React, { useState, useEffect } from 'react'
import { Users, UserCheck, Calendar, Newspaper, TrendingUp, Trophy } from 'lucide-react'
import StatsCard from '../components/shared/StatsCard'
import LoadingSpinner from '../components/shared/LoadingSpinner'
import { statsAPI, matchesAPI, newsAPI } from '../services/supabase'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'

const Dashboard = () => {
  const [stats, setStats] = useState({
    teams: 0,
    players: 0,
    matchesToday: 0,
    news: 0
  })
  const [recentMatches, setRecentMatches] = useState([])
  const [recentNews, setRecentNews] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      
      // Load stats
      const dashboardStats = await statsAPI.getDashboardStats()
      setStats(dashboardStats)

      // Load recent matches (last 5)
      const matches = await matchesAPI.getAll()
      setRecentMatches(matches.slice(0, 5))

      // Load recent news (last 5)
      const news = await newsAPI.getAll()
      setRecentNews(news.slice(0, 5))

    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getMatchStatusText = (status) => {
    const statusMap = {
      'scheduled': 'مجدولة',
      'live': 'مباشر',
      'finished': 'انتهت',
      'postponed': 'مؤجلة'
    }
    return statusMap[status] || status
  }

  const getMatchStatusColor = (status) => {
    const colorMap = {
      'scheduled': 'text-blue-600 bg-blue-100',
      'live': 'text-green-600 bg-green-100',
      'finished': 'text-secondary-600 bg-secondary-100',
      'postponed': 'text-orange-600 bg-orange-100'
    }
    return colorMap[status] || 'text-secondary-600 bg-secondary-100'
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-secondary-900">لوحة التحكم</h1>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <StatsCard key={i} loading={true} />
          ))}
        </div>
        
        <LoadingSpinner size="lg" className="py-12" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-secondary-900">لوحة التحكم</h1>
        <p className="text-secondary-600">
          {format(new Date(), 'EEEE، d MMMM yyyy', { locale: ar })}
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="إجمالي الفرق"
          value={stats.teams}
          icon={Users}
          color="primary"
        />
        <StatsCard
          title="إجمالي اللاعبين"
          value={stats.players}
          icon={UserCheck}
          color="green"
        />
        <StatsCard
          title="مباريات اليوم"
          value={stats.matchesToday}
          icon={Calendar}
          color="blue"
        />
        <StatsCard
          title="الأخبار"
          value={stats.news}
          icon={Newspaper}
          color="orange"
        />
      </div>

      {/* Recent Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Matches */}
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-secondary-900 flex items-center">
              <Calendar className="h-5 w-5 ml-2" />
              المباريات الأخيرة
            </h2>
            <a href="/matches" className="text-primary-600 hover:text-primary-800 text-sm">
              عرض الكل
            </a>
          </div>
          
          <div className="space-y-3">
            {recentMatches.length > 0 ? (
              recentMatches.map((match) => (
                <div key={match.id} className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className="text-center">
                      <p className="text-sm font-medium text-secondary-900">
                        {match.home_team?.name}
                      </p>
                      <p className="text-xs text-secondary-500">vs</p>
                      <p className="text-sm font-medium text-secondary-900">
                        {match.away_team?.name}
                      </p>
                    </div>
                  </div>
                  
                  <div className="text-left">
                    <span className={`
                      px-2 py-1 rounded-full text-xs font-medium
                      ${getMatchStatusColor(match.status)}
                    `}>
                      {getMatchStatusText(match.status)}
                    </span>
                    <p className="text-xs text-secondary-500 mt-1">
                      {format(new Date(match.match_date), 'dd/MM HH:mm')}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-secondary-500 text-center py-4">لا توجد مباريات</p>
            )}
          </div>
        </div>

        {/* Recent News */}
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-secondary-900 flex items-center">
              <Newspaper className="h-5 w-5 ml-2" />
              الأخبار الأخيرة
            </h2>
            <a href="/news" className="text-primary-600 hover:text-primary-800 text-sm">
              عرض الكل
            </a>
          </div>
          
          <div className="space-y-3">
            {recentNews.length > 0 ? (
              recentNews.map((news) => (
                <div key={news.id} className="flex items-start space-x-3 space-x-reverse p-3 bg-secondary-50 rounded-lg">
                  {news.image_url && (
                    <img 
                      src={news.image_url} 
                      alt={news.title}
                      className="w-12 h-12 rounded-lg object-cover flex-shrink-0"
                    />
                  )}
                  <div className="flex-1 min-w-0">
                    <h3 className="text-sm font-medium text-secondary-900 line-clamp-2">
                      {news.title}
                    </h3>
                    <p className="text-xs text-secondary-500 mt-1">
                      {format(new Date(news.created_at), 'dd/MM/yyyy HH:mm')}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-secondary-500 text-center py-4">لا توجد أخبار</p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
