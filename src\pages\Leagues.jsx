import React, { useState, useEffect } from 'react'
import { Plus, Search } from 'lucide-react'
import { toast } from 'react-hot-toast'
import Table from '../components/shared/Table'
import Modal from '../components/shared/Modal'
import { InputField, FormButtons } from '../components/shared/Form'
import { leaguesAPI } from '../services/supabase'

const Leagues = () => {
  const [leagues, setLeagues] = useState([])
  const [loading, setLoading] = useState(true)
  const [modalOpen, setModalOpen] = useState(false)
  const [editingLeague, setEditingLeague] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [formData, setFormData] = useState({
    name: '',
    country: '',
    logo_url: ''
  })
  const [formLoading, setFormLoading] = useState(false)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const data = await leaguesAPI.getAll()
      setLeagues(data)
    } catch (error) {
      console.error('Error loading leagues:', error)
      toast.error('حدث خطأ في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    try {
      setFormLoading(true)
      
      if (editingLeague) {
        await leaguesAPI.update(editingLeague.id, formData)
        toast.success('تم تحديث البطولة بنجاح')
      } else {
        await leaguesAPI.create(formData)
        toast.success('تم إضافة البطولة بنجاح')
      }
      
      await loadData()
      closeModal()
    } catch (error) {
      console.error('Error saving league:', error)
      toast.error('حدث خطأ في حفظ البطولة')
    } finally {
      setFormLoading(false)
    }
  }

  const handleEdit = (league) => {
    setEditingLeague(league)
    setFormData({
      name: league.name,
      country: league.country || '',
      logo_url: league.logo_url || ''
    })
    setModalOpen(true)
  }

  const handleDelete = async (league) => {
    if (window.confirm('هل أنت متأكد من حذف هذه البطولة؟')) {
      try {
        await leaguesAPI.delete(league.id)
        toast.success('تم حذف البطولة بنجاح')
        await loadData()
      } catch (error) {
        console.error('Error deleting league:', error)
        toast.error('حدث خطأ في حذف البطولة')
      }
    }
  }

  const openAddModal = () => {
    setEditingLeague(null)
    setFormData({
      name: '',
      country: '',
      logo_url: ''
    })
    setModalOpen(true)
  }

  const closeModal = () => {
    setModalOpen(false)
    setEditingLeague(null)
  }

  const filteredLeagues = leagues.filter(league =>
    league.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    league.country?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const columns = [
    {
      header: 'الشعار',
      key: 'logo_url',
      render: (value) => (
        value ? (
          <img src={value} alt="شعار البطولة" className="w-10 h-10 rounded object-cover" />
        ) : (
          <div className="w-10 h-10 bg-secondary-200 rounded flex items-center justify-center">
            <span className="text-secondary-500 text-xs">لا يوجد</span>
          </div>
        )
      )
    },
    {
      header: 'اسم البطولة',
      key: 'name'
    },
    {
      header: 'الدولة',
      key: 'country'
    },
    {
      header: 'تاريخ الإنشاء',
      key: 'created_at',
      render: (value) => new Date(value).toLocaleDateString('ar-SA')
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-secondary-900">إدارة البطولات</h1>
        <button
          onClick={openAddModal}
          className="btn-primary flex items-center"
        >
          <Plus className="h-5 w-5 ml-2" />
          إضافة بطولة
        </button>
      </div>

      {/* Search */}
      <div className="card">
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-400 h-5 w-5" />
          <input
            type="text"
            placeholder="البحث في البطولات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="input-field pr-10"
          />
        </div>
      </div>

      {/* Leagues Table */}
      <Table
        columns={columns}
        data={filteredLeagues}
        onEdit={handleEdit}
        onDelete={handleDelete}
        loading={loading}
        emptyMessage="لا توجد بطولات"
      />

      {/* Add/Edit Modal */}
      <Modal
        isOpen={modalOpen}
        onClose={closeModal}
        title={editingLeague ? 'تعديل البطولة' : 'إضافة بطولة جديدة'}
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <InputField
            label="اسم البطولة"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            placeholder="أدخل اسم البطولة"
            required
          />

          <InputField
            label="الدولة"
            name="country"
            value={formData.country}
            onChange={handleInputChange}
            placeholder="أدخل اسم الدولة"
          />

          <InputField
            label="رابط الشعار"
            name="logo_url"
            type="url"
            value={formData.logo_url}
            onChange={handleInputChange}
            placeholder="https://example.com/logo.png"
          />

          <FormButtons
            onSubmit={handleSubmit}
            onCancel={closeModal}
            loading={formLoading}
            submitText={editingLeague ? 'تحديث' : 'إضافة'}
          />
        </form>
      </Modal>
    </div>
  )
}

export default Leagues
