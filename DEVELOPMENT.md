# دليل التطوير - Development Guide

## 🔧 إعداد بيئة التطوير

### متطلبات النظام
- Node.js 16.0.0 أو أحدث
- npm 7.0.0 أو أحدث (أو yarn 1.22.0+)
- Git
- محرر نصوص (VS Code مُوصى به)

### إضافات VS Code المُوصى بها
```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

## 📊 إعداد Supabase

### 1. إنشاء المشروع
1. اذهب إلى [supabase.com](https://supabase.com)
2. أنشئ حساب جديد أو سجل دخول
3. اضغط "New Project"
4. اختر Organization واملأ تفاصيل المشروع
5. انتظر حتى يكتمل الإعداد (2-3 دقائق)

### 2. إعد<PERSON> قاعدة البيانات
1. اذهب إلى SQL Editor في لوحة تحكم Supabase
2. انسخ محتوى ملف `database-schema.sql`
3. الصق الكود واضغط "Run"
4. تأكد من إنشاء جميع الجداول بنجاح

### 3. إعداد Row Level Security (RLS)
```sql
-- تعطيل RLS للتطوير (يمكن تفعيله لاحقاً للأمان)
ALTER TABLE leagues DISABLE ROW LEVEL SECURITY;
ALTER TABLE teams DISABLE ROW LEVEL SECURITY;
ALTER TABLE players DISABLE ROW LEVEL SECURITY;
ALTER TABLE matches DISABLE ROW LEVEL SECURITY;
ALTER TABLE news DISABLE ROW LEVEL SECURITY;
ALTER TABLE standings DISABLE ROW LEVEL SECURITY;
ALTER TABLE match_stats DISABLE ROW LEVEL SECURITY;
```

### 4. الحصول على API Keys
1. اذهب إلى Settings > API
2. انسخ:
   - Project URL
   - anon/public key

## 🗂️ هيكل الملفات التفصيلي

```
sports-admin-panel/
├── public/
│   ├── vite.svg
│   └── index.html
├── src/
│   ├── components/
│   │   ├── shared/
│   │   │   ├── Table.jsx          # جدول قابل للإعادة الاستخدام
│   │   │   ├── Modal.jsx          # نافذة منبثقة
│   │   │   ├── Form.jsx           # مكونات النماذج
│   │   │   ├── LoadingSpinner.jsx # مؤشر التحميل
│   │   │   └── StatsCard.jsx      # بطاقة الإحصائيات
│   │   ├── Layout.jsx             # تخطيط الصفحة الرئيسي
│   │   ├── Header.jsx             # رأس الصفحة
│   │   └── Sidebar.jsx            # القائمة الجانبية
│   ├── pages/
│   │   ├── Dashboard.jsx          # الصفحة الرئيسية
│   │   ├── Matches.jsx            # إدارة المباريات
│   │   ├── Teams.jsx              # إدارة الفرق
│   │   ├── Players.jsx            # إدارة اللاعبين
│   │   ├── Leagues.jsx            # إدارة البطولات
│   │   ├── News.jsx               # إدارة الأخبار
│   │   ├── Standings.jsx          # جدول الترتيب
│   │   └── MatchStats.jsx         # إحصائيات المباريات
│   ├── services/
│   │   └── supabase.js            # API functions
│   ├── App.jsx                    # المكون الرئيسي
│   ├── main.jsx                   # نقطة الدخول
│   └── index.css                  # الأنماط الرئيسية
├── database-schema.sql            # هيكل قاعدة البيانات
├── package.json
├── vite.config.js
├── tailwind.config.js
├── postcss.config.js
├── .env.example
└── README.md
```

## 🎨 نظام التصميم

### الألوان
```css
/* الألوان الأساسية */
primary-50: #eff6ff    /* خلفية فاتحة */
primary-500: #3b82f6   /* اللون الأساسي */
primary-600: #2563eb   /* أزرار */
primary-700: #1d4ed8   /* hover */

/* الألوان الثانوية */
secondary-50: #f8fafc   /* خلفية */
secondary-500: #64748b  /* نص ثانوي */
secondary-900: #0f172a  /* نص رئيسي */
```

### المكونات الأساسية
```css
/* أزرار */
.btn-primary    /* زر أساسي أزرق */
.btn-secondary  /* زر ثانوي رمادي */
.btn-danger     /* زر خطر أحمر */

/* بطاقات */
.card           /* بطاقة بيضاء مع ظل */

/* حقول الإدخال */
.input-field    /* حقل إدخال موحد */

/* جداول */
.table-header   /* رأس الجدول */
.table-cell     /* خلية الجدول */
```

## 🔄 إدارة الحالة

### استخدام useState للحالة المحلية
```jsx
const [data, setData] = useState([])
const [loading, setLoading] = useState(true)
const [error, setError] = useState(null)
```

### نمط تحميل البيانات
```jsx
useEffect(() => {
  const loadData = async () => {
    try {
      setLoading(true)
      const result = await apiFunction()
      setData(result)
    } catch (error) {
      setError(error.message)
      toast.error('حدث خطأ في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }
  
  loadData()
}, [])
```

## 📡 API Functions

### هيكل دوال API
```jsx
export const entityAPI = {
  // جلب جميع العناصر
  getAll: async () => {
    const { data, error } = await supabase
      .from('table_name')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  },

  // إضافة عنصر جديد
  create: async (item) => {
    const { data, error } = await supabase
      .from('table_name')
      .insert([item])
      .select()
    
    if (error) throw error
    return data[0]
  },

  // تحديث عنصر
  update: async (id, updates) => {
    const { data, error } = await supabase
      .from('table_name')
      .update(updates)
      .eq('id', id)
      .select()
    
    if (error) throw error
    return data[0]
  },

  // حذف عنصر
  delete: async (id) => {
    const { error } = await supabase
      .from('table_name')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  }
}
```

## 🧪 اختبار المشروع

### بيانات تجريبية
```sql
-- إضافة بطولة تجريبية
INSERT INTO leagues (name, country, logo_url) VALUES 
('الدوري السعودي للمحترفين', 'السعودية', 'https://example.com/spl-logo.png');

-- إضافة فرق تجريبية
INSERT INTO teams (name, country, league_id, stadium) VALUES 
('الهلال', 'السعودية', 'league_id_here', 'ملعب الملك فهد الدولي'),
('النصر', 'السعودية', 'league_id_here', 'ملعب مرسول بارك');

-- إضافة مباراة تجريبية
INSERT INTO matches (home_team_id, away_team_id, league_id, match_date, status) VALUES 
('home_team_id', 'away_team_id', 'league_id', '2024-01-15 20:00:00', 'scheduled');
```

## 🚀 نشر المشروع

### Vercel (مُوصى به)
1. ادفع الكود إلى GitHub
2. اربط المستودع بـ Vercel
3. أضف متغيرات البيئة في إعدادات Vercel
4. انشر المشروع

### Netlify
1. ابني المشروع: `npm run build`
2. ارفع مجلد `dist` إلى Netlify
3. أضف متغيرات البيئة

## 🔒 الأمان

### إعدادات RLS للإنتاج
```sql
-- تفعيل RLS
ALTER TABLE leagues ENABLE ROW LEVEL SECURITY;

-- سياسة للقراءة (مثال)
CREATE POLICY "Enable read access for all users" ON leagues
FOR SELECT USING (true);

-- سياسة للكتابة (للمدراء فقط)
CREATE POLICY "Enable insert for authenticated users only" ON leagues
FOR INSERT WITH CHECK (auth.role() = 'authenticated');
```

## 🐛 استكشاف الأخطاء

### أخطاء شائعة
1. **خطأ في الاتصال بـ Supabase**: تأكد من صحة URL و API Key
2. **خطأ في استيراد المكونات**: تأكد من مسارات الاستيراد
3. **خطأ في Tailwind**: تأكد من تشغيل `npm run dev` بعد تغيير التكوين

### أدوات التطوير
```jsx
// تفعيل وضع التطوير في Supabase
const supabase = createClient(url, key, {
  auth: {
    debug: true
  }
})
```

## 📈 تحسين الأداء

### تحسينات مُوصى بها
1. استخدام `React.memo` للمكونات الثقيلة
2. تحسين الصور (WebP, lazy loading)
3. تقسيم الكود (code splitting)
4. استخدام Service Workers للتخزين المؤقت

---

هذا الدليل يغطي الأساسيات للبدء في التطوير. للمزيد من التفاصيل، راجع التوثيق الرسمي للتقنيات المستخدمة.
