import React from 'react'

const StatsCard = ({ 
  title, 
  value, 
  icon: Icon, 
  color = 'primary',
  trend,
  trendValue,
  loading = false
}) => {
  const colorClasses = {
    primary: {
      bg: 'bg-primary-500',
      text: 'text-primary-600',
      lightBg: 'bg-primary-50'
    },
    green: {
      bg: 'bg-green-500',
      text: 'text-green-600',
      lightBg: 'bg-green-50'
    },
    blue: {
      bg: 'bg-blue-500',
      text: 'text-blue-600',
      lightBg: 'bg-blue-50'
    },
    orange: {
      bg: 'bg-orange-500',
      text: 'text-orange-600',
      lightBg: 'bg-orange-50'
    },
    red: {
      bg: 'bg-red-500',
      text: 'text-red-600',
      lightBg: 'bg-red-50'
    }
  }

  const colors = colorClasses[color]

  if (loading) {
    return (
      <div className="card">
        <div className="animate-pulse">
          <div className="flex items-center">
            <div className="h-12 w-12 bg-secondary-200 rounded-lg"></div>
            <div className="mr-4 flex-1">
              <div className="h-4 bg-secondary-200 rounded w-3/4 mb-2"></div>
              <div className="h-6 bg-secondary-200 rounded w-1/2"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="card">
      <div className="flex items-center">
        <div className={`
          flex-shrink-0 p-3 rounded-lg ${colors.lightBg}
        `}>
          <Icon className={`h-6 w-6 ${colors.text}`} />
        </div>
        
        <div className="mr-4 flex-1">
          <p className="text-sm font-medium text-secondary-600">
            {title}
          </p>
          <p className="text-2xl font-bold text-secondary-900">
            {value?.toLocaleString('ar-SA') || '0'}
          </p>
          
          {trend && trendValue && (
            <div className="flex items-center mt-1">
              <span className={`
                text-xs font-medium
                ${trend === 'up' ? 'text-green-600' : 'text-red-600'}
              `}>
                {trend === 'up' ? '↗' : '↘'} {trendValue}
              </span>
              <span className="text-xs text-secondary-500 mr-1">
                من الشهر الماضي
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default StatsCard
