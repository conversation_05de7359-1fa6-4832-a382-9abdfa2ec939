import React, { useState, useEffect } from 'react'
import { Search, BarChart3 } from 'lucide-react'
import { toast } from 'react-hot-toast'
import Modal from '../components/shared/Modal'
import { InputField, SelectField, FormButtons } from '../components/shared/Form'
import { matchStatsAPI, matchesAPI } from '../services/supabase'
import { format } from 'date-fns'

const MatchStats = () => {
  const [matches, setMatches] = useState([])
  const [selectedMatch, setSelectedMatch] = useState('')
  const [matchStats, setMatchStats] = useState(null)
  const [loading, setLoading] = useState(true)
  const [modalOpen, setModalOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [formData, setFormData] = useState({
    match_id: '',
    home_possession: 50,
    away_possession: 50,
    home_shots: 0,
    away_shots: 0,
    home_shots_on_target: 0,
    away_shots_on_target: 0,
    home_corners: 0,
    away_corners: 0,
    home_fouls: 0,
    away_fouls: 0,
    home_yellow_cards: 0,
    away_yellow_cards: 0,
    home_red_cards: 0,
    away_red_cards: 0
  })
  const [formLoading, setFormLoading] = useState(false)

  useEffect(() => {
    loadMatches()
  }, [])

  useEffect(() => {
    if (selectedMatch) {
      loadMatchStats()
    }
  }, [selectedMatch])

  const loadMatches = async () => {
    try {
      setLoading(true)
      const data = await matchesAPI.getAll()
      setMatches(data.filter(match => match.status === 'finished'))
    } catch (error) {
      console.error('Error loading matches:', error)
      toast.error('حدث خطأ في تحميل المباريات')
    } finally {
      setLoading(false)
    }
  }

  const loadMatchStats = async () => {
    try {
      const stats = await matchStatsAPI.getByMatch(selectedMatch)
      setMatchStats(stats)
    } catch (error) {
      console.error('Error loading match stats:', error)
      setMatchStats(null)
    }
  }

  const handleMatchChange = (e) => {
    setSelectedMatch(e.target.value)
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => {
      const newData = {
        ...prev,
        [name]: parseInt(value) || 0
      }
      
      // Auto-calculate possession percentages
      if (name === 'home_possession') {
        newData.away_possession = 100 - parseInt(value)
      } else if (name === 'away_possession') {
        newData.home_possession = 100 - parseInt(value)
      }
      
      return newData
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    try {
      setFormLoading(true)
      
      await matchStatsAPI.upsert({
        ...formData,
        match_id: selectedMatch
      })
      
      toast.success('تم حفظ الإحصائيات بنجاح')
      await loadMatchStats()
      closeModal()
    } catch (error) {
      console.error('Error saving match stats:', error)
      toast.error('حدث خطأ في حفظ الإحصائيات')
    } finally {
      setFormLoading(false)
    }
  }

  const openStatsModal = () => {
    const match = matches.find(m => m.id === selectedMatch)
    if (!match) return

    setFormData({
      match_id: selectedMatch,
      home_possession: matchStats?.home_possession || 50,
      away_possession: matchStats?.away_possession || 50,
      home_shots: matchStats?.home_shots || 0,
      away_shots: matchStats?.away_shots || 0,
      home_shots_on_target: matchStats?.home_shots_on_target || 0,
      away_shots_on_target: matchStats?.away_shots_on_target || 0,
      home_corners: matchStats?.home_corners || 0,
      away_corners: matchStats?.away_corners || 0,
      home_fouls: matchStats?.home_fouls || 0,
      away_fouls: matchStats?.away_fouls || 0,
      home_yellow_cards: matchStats?.home_yellow_cards || 0,
      away_yellow_cards: matchStats?.away_yellow_cards || 0,
      home_red_cards: matchStats?.home_red_cards || 0,
      away_red_cards: matchStats?.away_red_cards || 0
    })
    setModalOpen(true)
  }

  const closeModal = () => {
    setModalOpen(false)
  }

  const filteredMatches = matches.filter(match =>
    match.home_team?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    match.away_team?.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const matchOptions = filteredMatches.map(match => ({
    value: match.id,
    label: `${match.home_team?.name} vs ${match.away_team?.name} - ${format(new Date(match.match_date), 'dd/MM/yyyy')}`
  }))

  const selectedMatchData = matches.find(m => m.id === selectedMatch)

  const StatItem = ({ label, homeValue, awayValue, isPercentage = false }) => (
    <div className="bg-secondary-50 rounded-lg p-4">
      <h4 className="text-sm font-medium text-secondary-600 mb-2 text-center">{label}</h4>
      <div className="flex justify-between items-center">
        <div className="text-center">
          <span className="text-2xl font-bold text-primary-600">
            {homeValue}{isPercentage ? '%' : ''}
          </span>
          <p className="text-xs text-secondary-500">{selectedMatchData?.home_team?.name}</p>
        </div>
        <div className="text-center">
          <span className="text-2xl font-bold text-blue-600">
            {awayValue}{isPercentage ? '%' : ''}
          </span>
          <p className="text-xs text-secondary-500">{selectedMatchData?.away_team?.name}</p>
        </div>
      </div>
    </div>
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-secondary-900">إحصائيات المباريات</h1>
        {selectedMatch && (
          <button
            onClick={openStatsModal}
            className="btn-primary flex items-center"
          >
            <BarChart3 className="h-5 w-5 ml-2" />
            {matchStats ? 'تعديل الإحصائيات' : 'إضافة إحصائيات'}
          </button>
        )}
      </div>

      {/* Match Selection */}
      <div className="card">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-400 h-5 w-5" />
            <input
              type="text"
              placeholder="البحث في المباريات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input-field pr-10"
            />
          </div>
          
          <SelectField
            label="اختر المباراة"
            name="match"
            value={selectedMatch}
            onChange={handleMatchChange}
            options={matchOptions}
            placeholder="اختر المباراة"
          />
        </div>
      </div>

      {/* Match Stats Display */}
      {selectedMatch && selectedMatchData && (
        <div className="card">
          {/* Match Header */}
          <div className="text-center mb-6 pb-6 border-b border-secondary-200">
            <div className="flex items-center justify-center space-x-4 space-x-reverse mb-2">
              <div className="text-center">
                {selectedMatchData.home_team?.logo_url && (
                  <img src={selectedMatchData.home_team.logo_url} alt="" className="w-12 h-12 mx-auto mb-2" />
                )}
                <h3 className="font-semibold">{selectedMatchData.home_team?.name}</h3>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-secondary-900">
                  {selectedMatchData.home_score} - {selectedMatchData.away_score}
                </div>
                <p className="text-sm text-secondary-500">
                  {format(new Date(selectedMatchData.match_date), 'dd/MM/yyyy')}
                </p>
              </div>
              
              <div className="text-center">
                {selectedMatchData.away_team?.logo_url && (
                  <img src={selectedMatchData.away_team.logo_url} alt="" className="w-12 h-12 mx-auto mb-2" />
                )}
                <h3 className="font-semibold">{selectedMatchData.away_team?.name}</h3>
              </div>
            </div>
          </div>

          {/* Stats Grid */}
          {matchStats ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <StatItem 
                label="الاستحواذ" 
                homeValue={matchStats.home_possession} 
                awayValue={matchStats.away_possession} 
                isPercentage={true}
              />
              <StatItem 
                label="التسديدات" 
                homeValue={matchStats.home_shots} 
                awayValue={matchStats.away_shots} 
              />
              <StatItem 
                label="التسديدات على المرمى" 
                homeValue={matchStats.home_shots_on_target} 
                awayValue={matchStats.away_shots_on_target} 
              />
              <StatItem 
                label="الركنيات" 
                homeValue={matchStats.home_corners} 
                awayValue={matchStats.away_corners} 
              />
              <StatItem 
                label="الأخطاء" 
                homeValue={matchStats.home_fouls} 
                awayValue={matchStats.away_fouls} 
              />
              <StatItem 
                label="البطاقات الصفراء" 
                homeValue={matchStats.home_yellow_cards} 
                awayValue={matchStats.away_yellow_cards} 
              />
            </div>
          ) : (
            <div className="text-center py-12">
              <BarChart3 className="h-16 w-16 text-secondary-300 mx-auto mb-4" />
              <p className="text-secondary-500 text-lg">لا توجد إحصائيات لهذه المباراة</p>
              <p className="text-secondary-400 text-sm">اضغط على "إضافة إحصائيات" لإضافة البيانات</p>
            </div>
          )}
        </div>
      )}

      {/* Stats Modal */}
      <Modal
        isOpen={modalOpen}
        onClose={closeModal}
        title="إحصائيات المباراة"
        size="xl"
      >
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Possession */}
          <div>
            <h4 className="font-medium text-secondary-900 mb-3">الاستحواذ</h4>
            <div className="grid grid-cols-2 gap-4">
              <InputField
                label={`${selectedMatchData?.home_team?.name} (%)`}
                name="home_possession"
                type="number"
                min="0"
                max="100"
                value={formData.home_possession}
                onChange={handleInputChange}
              />
              <InputField
                label={`${selectedMatchData?.away_team?.name} (%)`}
                name="away_possession"
                type="number"
                min="0"
                max="100"
                value={formData.away_possession}
                onChange={handleInputChange}
              />
            </div>
          </div>

          {/* Shots */}
          <div>
            <h4 className="font-medium text-secondary-900 mb-3">التسديدات</h4>
            <div className="grid grid-cols-2 gap-4">
              <InputField
                label={`${selectedMatchData?.home_team?.name} - التسديدات`}
                name="home_shots"
                type="number"
                min="0"
                value={formData.home_shots}
                onChange={handleInputChange}
              />
              <InputField
                label={`${selectedMatchData?.away_team?.name} - التسديدات`}
                name="away_shots"
                type="number"
                min="0"
                value={formData.away_shots}
                onChange={handleInputChange}
              />
              <InputField
                label={`${selectedMatchData?.home_team?.name} - على المرمى`}
                name="home_shots_on_target"
                type="number"
                min="0"
                value={formData.home_shots_on_target}
                onChange={handleInputChange}
              />
              <InputField
                label={`${selectedMatchData?.away_team?.name} - على المرمى`}
                name="away_shots_on_target"
                type="number"
                min="0"
                value={formData.away_shots_on_target}
                onChange={handleInputChange}
              />
            </div>
          </div>

          {/* Other Stats */}
          <div>
            <h4 className="font-medium text-secondary-900 mb-3">إحصائيات أخرى</h4>
            <div className="grid grid-cols-2 gap-4">
              <InputField
                label={`${selectedMatchData?.home_team?.name} - الركنيات`}
                name="home_corners"
                type="number"
                min="0"
                value={formData.home_corners}
                onChange={handleInputChange}
              />
              <InputField
                label={`${selectedMatchData?.away_team?.name} - الركنيات`}
                name="away_corners"
                type="number"
                min="0"
                value={formData.away_corners}
                onChange={handleInputChange}
              />
              <InputField
                label={`${selectedMatchData?.home_team?.name} - الأخطاء`}
                name="home_fouls"
                type="number"
                min="0"
                value={formData.home_fouls}
                onChange={handleInputChange}
              />
              <InputField
                label={`${selectedMatchData?.away_team?.name} - الأخطاء`}
                name="away_fouls"
                type="number"
                min="0"
                value={formData.away_fouls}
                onChange={handleInputChange}
              />
              <InputField
                label={`${selectedMatchData?.home_team?.name} - البطاقات الصفراء`}
                name="home_yellow_cards"
                type="number"
                min="0"
                value={formData.home_yellow_cards}
                onChange={handleInputChange}
              />
              <InputField
                label={`${selectedMatchData?.away_team?.name} - البطاقات الصفراء`}
                name="away_yellow_cards"
                type="number"
                min="0"
                value={formData.away_yellow_cards}
                onChange={handleInputChange}
              />
              <InputField
                label={`${selectedMatchData?.home_team?.name} - البطاقات الحمراء`}
                name="home_red_cards"
                type="number"
                min="0"
                value={formData.home_red_cards}
                onChange={handleInputChange}
              />
              <InputField
                label={`${selectedMatchData?.away_team?.name} - البطاقات الحمراء`}
                name="away_red_cards"
                type="number"
                min="0"
                value={formData.away_red_cards}
                onChange={handleInputChange}
              />
            </div>
          </div>

          <FormButtons
            onSubmit={handleSubmit}
            onCancel={closeModal}
            loading={formLoading}
            submitText="حفظ الإحصائيات"
          />
        </form>
      </Modal>
    </div>
  )
}

export default MatchStats
