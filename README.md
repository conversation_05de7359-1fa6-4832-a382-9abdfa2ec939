# لوحة التحكم الرياضية - Sports Admin Panel

لوحة تحكم شاملة لإدارة التطبيقات الرياضية مبنية بـ React و Tailwind CSS مع قاعدة بيانات Supabase.

## 🚀 المميزات

### 📊 لوحة التحكم الرئيسية
- عرض الإحصائيات العامة (عدد الفرق، اللاعبين، المباريات، الأخبار)
- المباريات الأخيرة والأخبار الحديثة
- واجهة سهلة الاستخدام باللغة العربية

### ⚽ إدارة المباريات
- إضافة وتعديل وحذف المباريات
- ربط المباريات بالفرق والبطولات
- تتبع النتائج والحالة (مجدولة، مباشر، انتهت، مؤجلة)
- إدارة تفاصيل المباراة (الملعب، الحكم، الحضور)

### 🏆 إدارة الفرق
- إضافة وإدارة الفرق الرياضية
- رفع شعارات الفرق
- ربط الفرق بالبطولات
- معلومات تفصيلية (سنة التأسيس، الملعب، الدولة)

### 👥 إدارة اللاعبين
- قاعدة بيانات شاملة للاعبين
- صور اللاعبين ومعلوماتهم الشخصية
- المراكز والأرقام والقيمة السوقية
- ربط اللاعبين بالفرق

### 🏅 إدارة البطولات
- إنشاء وإدارة البطولات والدوريات
- شعارات البطولات
- تصنيف حسب الدولة

### 📰 إدارة الأخبار
- نظام إدارة محتوى للأخبار الرياضية
- رفع الصور والمحتوى
- حالة النشر (منشور/مسودة)
- معلومات الكاتب والتاريخ

### 📈 جدول الترتيب
- عرض ترتيب الفرق في كل بطولة
- حساب النقاط وفارق الأهداف
- مؤشرات بصرية للمراكز المهمة

### 📊 إحصائيات المباريات
- إحصائيات تفصيلية لكل مباراة
- الاستحواذ، التسديدات، الركنيات
- البطاقات والأخطاء
- واجهة مقارنة بين الفريقين

## 🛠️ التقنيات المستخدمة

- **Frontend**: React 18, Vite
- **Styling**: Tailwind CSS
- **Database**: Supabase (PostgreSQL)
- **Icons**: Lucide React
- **Routing**: React Router DOM
- **Notifications**: React Hot Toast
- **Date Handling**: date-fns

## 📋 متطلبات التشغيل

- Node.js 16+ 
- npm أو yarn
- حساب Supabase

## ⚙️ التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd sports-admin-panel
```

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. إعداد قاعدة البيانات
1. أنشئ مشروع جديد في [Supabase](https://supabase.com)
2. انسخ محتوى ملف `database-schema.sql` وشغله في SQL Editor
3. احصل على URL و API Key من إعدادات المشروع

### 4. إعداد متغيرات البيئة
```bash
cp .env.example .env
```

عدل ملف `.env` وأضف بيانات Supabase:
```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 5. تشغيل المشروع
```bash
npm run dev
```

سيعمل المشروع على `http://localhost:3000`

## 📁 هيكل المشروع

```
src/
├── components/
│   ├── shared/          # مكونات مشتركة
│   ├── Layout.jsx       # تخطيط الصفحة
│   ├── Header.jsx       # رأس الصفحة
│   └── Sidebar.jsx      # الشريط الجانبي
├── pages/               # صفحات التطبيق
├── services/            # خدمات API
└── index.css           # أنماط CSS
```

## 🔧 إعداد قاعدة البيانات

قاعدة البيانات تحتوي على الجداول التالية:
- `leagues` - البطولات
- `teams` - الفرق
- `players` - اللاعبين
- `matches` - المباريات
- `news` - الأخبار
- `standings` - جدول الترتيب
- `match_stats` - إحصائيات المباريات

## 🎨 التخصيص

### الألوان
يمكن تخصيص الألوان من ملف `tailwind.config.js`:
```js
colors: {
  primary: { /* ألوان أساسية */ },
  secondary: { /* ألوان ثانوية */ }
}
```

### الخطوط
الخطوط العربية محددة في `src/index.css`:
```css
font-family: 'Cairo', 'Tajawal', sans-serif;
```

## 🚀 البناء للإنتاج

```bash
npm run build
```

## 📝 المميزات المستقبلية

- [ ] تسجيل دخول آمن للمدراء
- [ ] رفع الصور إلى Cloudinary
- [ ] الوضع الليلي/النهاري
- [ ] إشعارات فورية
- [ ] تصدير البيانات
- [ ] تقارير متقدمة
- [ ] API للتطبيقات الخارجية

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## 📄 الرخصة

هذا المشروع مرخص تحت رخصة MIT.

## 📞 الدعم

للدعم والاستفسارات، يرجى إنشاء Issue في GitHub أو التواصل عبر البريد الإلكتروني.

---

تم تطوير هذا المشروع بـ ❤️ للمجتمع الرياضي العربي
