import React, { useState, useEffect } from 'react'
import { Plus, Search } from 'lucide-react'
import { toast } from 'react-hot-toast'
import Table from '../components/shared/Table'
import Modal from '../components/shared/Modal'
import { InputField, SelectField, FormButtons } from '../components/shared/Form'
import { matchesAPI, teamsAPI, leaguesAPI } from '../services/supabase'
import { format } from 'date-fns'

const Matches = () => {
  const [matches, setMatches] = useState([])
  const [teams, setTeams] = useState([])
  const [leagues, setLeagues] = useState([])
  const [loading, setLoading] = useState(true)
  const [modalOpen, setModalOpen] = useState(false)
  const [editingMatch, setEditingMatch] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [formData, setFormData] = useState({
    home_team_id: '',
    away_team_id: '',
    league_id: '',
    match_date: '',
    status: 'scheduled',
    home_score: 0,
    away_score: 0,
    venue: '',
    referee: ''
  })
  const [formLoading, setFormLoading] = useState(false)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const [matchesData, teamsData, leaguesData] = await Promise.all([
        matchesAPI.getAll(),
        teamsAPI.getAll(),
        leaguesAPI.getAll()
      ])
      
      setMatches(matchesData)
      setTeams(teamsData)
      setLeagues(leaguesData)
    } catch (error) {
      console.error('Error loading data:', error)
      toast.error('حدث خطأ في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (formData.home_team_id === formData.away_team_id) {
      toast.error('لا يمكن أن يلعب الفريق ضد نفسه')
      return
    }

    try {
      setFormLoading(true)
      
      if (editingMatch) {
        await matchesAPI.update(editingMatch.id, formData)
        toast.success('تم تحديث المباراة بنجاح')
      } else {
        await matchesAPI.create(formData)
        toast.success('تم إضافة المباراة بنجاح')
      }
      
      await loadData()
      closeModal()
    } catch (error) {
      console.error('Error saving match:', error)
      toast.error('حدث خطأ في حفظ المباراة')
    } finally {
      setFormLoading(false)
    }
  }

  const handleEdit = (match) => {
    setEditingMatch(match)
    setFormData({
      home_team_id: match.home_team_id,
      away_team_id: match.away_team_id,
      league_id: match.league_id,
      match_date: format(new Date(match.match_date), "yyyy-MM-dd'T'HH:mm"),
      status: match.status,
      home_score: match.home_score || 0,
      away_score: match.away_score || 0,
      venue: match.venue || '',
      referee: match.referee || ''
    })
    setModalOpen(true)
  }

  const handleDelete = async (match) => {
    if (window.confirm('هل أنت متأكد من حذف هذه المباراة؟')) {
      try {
        await matchesAPI.delete(match.id)
        toast.success('تم حذف المباراة بنجاح')
        await loadData()
      } catch (error) {
        console.error('Error deleting match:', error)
        toast.error('حدث خطأ في حذف المباراة')
      }
    }
  }

  const openAddModal = () => {
    setEditingMatch(null)
    setFormData({
      home_team_id: '',
      away_team_id: '',
      league_id: '',
      match_date: '',
      status: 'scheduled',
      home_score: 0,
      away_score: 0,
      venue: '',
      referee: ''
    })
    setModalOpen(true)
  }

  const closeModal = () => {
    setModalOpen(false)
    setEditingMatch(null)
  }

  const getMatchStatusText = (status) => {
    const statusMap = {
      'scheduled': 'مجدولة',
      'live': 'مباشر',
      'finished': 'انتهت',
      'postponed': 'مؤجلة'
    }
    return statusMap[status] || status
  }

  const getMatchStatusColor = (status) => {
    const colorMap = {
      'scheduled': 'text-blue-600 bg-blue-100',
      'live': 'text-green-600 bg-green-100',
      'finished': 'text-secondary-600 bg-secondary-100',
      'postponed': 'text-orange-600 bg-orange-100'
    }
    return colorMap[status] || 'text-secondary-600 bg-secondary-100'
  }

  const filteredMatches = matches.filter(match =>
    match.home_team?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    match.away_team?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    match.leagues?.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const columns = [
    {
      header: 'الفريق المضيف',
      key: 'home_team',
      render: (value, row) => (
        <div className="flex items-center space-x-2 space-x-reverse">
          {row.home_team?.logo_url && (
            <img src={row.home_team.logo_url} alt="" className="w-6 h-6 rounded" />
          )}
          <span>{row.home_team?.name}</span>
        </div>
      )
    },
    {
      header: 'النتيجة',
      key: 'score',
      render: (value, row) => (
        <span className="font-mono text-lg">
          {row.home_score} - {row.away_score}
        </span>
      )
    },
    {
      header: 'الفريق الضيف',
      key: 'away_team',
      render: (value, row) => (
        <div className="flex items-center space-x-2 space-x-reverse">
          {row.away_team?.logo_url && (
            <img src={row.away_team.logo_url} alt="" className="w-6 h-6 rounded" />
          )}
          <span>{row.away_team?.name}</span>
        </div>
      )
    },
    {
      header: 'البطولة',
      key: 'league',
      render: (value, row) => row.leagues?.name
    },
    {
      header: 'التاريخ',
      key: 'match_date',
      render: (value) => format(new Date(value), 'dd/MM/yyyy HH:mm')
    },
    {
      header: 'الحالة',
      key: 'status',
      render: (value) => (
        <span className={`
          px-2 py-1 rounded-full text-xs font-medium
          ${getMatchStatusColor(value)}
        `}>
          {getMatchStatusText(value)}
        </span>
      )
    }
  ]

  const teamOptions = teams.map(team => ({
    value: team.id,
    label: team.name
  }))

  const leagueOptions = leagues.map(league => ({
    value: league.id,
    label: league.name
  }))

  const statusOptions = [
    { value: 'scheduled', label: 'مجدولة' },
    { value: 'live', label: 'مباشر' },
    { value: 'finished', label: 'انتهت' },
    { value: 'postponed', label: 'مؤجلة' }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-secondary-900">إدارة المباريات</h1>
        <button
          onClick={openAddModal}
          className="btn-primary flex items-center"
        >
          <Plus className="h-5 w-5 ml-2" />
          إضافة مباراة
        </button>
      </div>

      {/* Search */}
      <div className="card">
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-400 h-5 w-5" />
          <input
            type="text"
            placeholder="البحث في المباريات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="input-field pr-10"
          />
        </div>
      </div>

      {/* Matches Table */}
      <Table
        columns={columns}
        data={filteredMatches}
        onEdit={handleEdit}
        onDelete={handleDelete}
        loading={loading}
        emptyMessage="لا توجد مباريات"
      />

      {/* Add/Edit Modal */}
      <Modal
        isOpen={modalOpen}
        onClose={closeModal}
        title={editingMatch ? 'تعديل المباراة' : 'إضافة مباراة جديدة'}
        size="lg"
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <SelectField
              label="الفريق المضيف"
              name="home_team_id"
              value={formData.home_team_id}
              onChange={handleInputChange}
              options={teamOptions}
              required
            />
            
            <SelectField
              label="الفريق الضيف"
              name="away_team_id"
              value={formData.away_team_id}
              onChange={handleInputChange}
              options={teamOptions}
              required
            />
          </div>

          <SelectField
            label="البطولة"
            name="league_id"
            value={formData.league_id}
            onChange={handleInputChange}
            options={leagueOptions}
            required
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <InputField
              label="تاريخ ووقت المباراة"
              name="match_date"
              type="datetime-local"
              value={formData.match_date}
              onChange={handleInputChange}
              required
            />
            
            <SelectField
              label="حالة المباراة"
              name="status"
              value={formData.status}
              onChange={handleInputChange}
              options={statusOptions}
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <InputField
              label="أهداف الفريق المضيف"
              name="home_score"
              type="number"
              min="0"
              value={formData.home_score}
              onChange={handleInputChange}
            />
            
            <InputField
              label="أهداف الفريق الضيف"
              name="away_score"
              type="number"
              min="0"
              value={formData.away_score}
              onChange={handleInputChange}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <InputField
              label="الملعب"
              name="venue"
              value={formData.venue}
              onChange={handleInputChange}
              placeholder="اسم الملعب"
            />
            
            <InputField
              label="الحكم"
              name="referee"
              value={formData.referee}
              onChange={handleInputChange}
              placeholder="اسم الحكم"
            />
          </div>

          <FormButtons
            onSubmit={handleSubmit}
            onCancel={closeModal}
            loading={formLoading}
            submitText={editingMatch ? 'تحديث' : 'إضافة'}
          />
        </form>
      </Modal>
    </div>
  )
}

export default Matches
