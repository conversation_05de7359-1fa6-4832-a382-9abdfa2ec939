import { createClient } from '@supabase/supabase-js'

// إعدادات Supabase من متغيرات البيئة
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env file.')
}

export const supabase = createClient(supabaseUrl, supabaseKey)

// دوال مساعدة للتعامل مع قاعدة البيانات

// ===== إدارة البطولات =====
export const leaguesAPI = {
  // جلب جميع البطولات
  getAll: async () => {
    const { data, error } = await supabase
      .from('leagues')
      .select('*')
      .order('name')
    
    if (error) throw error
    return data
  },

  // إضافة بطولة جديدة
  create: async (league) => {
    const { data, error } = await supabase
      .from('leagues')
      .insert([league])
      .select()
    
    if (error) throw error
    return data[0]
  },

  // تحديث بطولة
  update: async (id, updates) => {
    const { data, error } = await supabase
      .from('leagues')
      .update(updates)
      .eq('id', id)
      .select()
    
    if (error) throw error
    return data[0]
  },

  // حذف بطولة
  delete: async (id) => {
    const { error } = await supabase
      .from('leagues')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  }
}

// ===== إدارة الفرق =====
export const teamsAPI = {
  // جلب جميع الفرق مع معلومات البطولة
  getAll: async () => {
    const { data, error } = await supabase
      .from('teams')
      .select(`
        *,
        leagues (
          id,
          name
        )
      `)
      .order('name')
    
    if (error) throw error
    return data
  },

  // إضافة فريق جديد
  create: async (team) => {
    const { data, error } = await supabase
      .from('teams')
      .insert([team])
      .select()
    
    if (error) throw error
    return data[0]
  },

  // تحديث فريق
  update: async (id, updates) => {
    const { data, error } = await supabase
      .from('teams')
      .update(updates)
      .eq('id', id)
      .select()
    
    if (error) throw error
    return data[0]
  },

  // حذف فريق
  delete: async (id) => {
    const { error } = await supabase
      .from('teams')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

// ===== إدارة اللاعبين =====
export const playersAPI = {
  // جلب جميع اللاعبين مع معلومات الفريق
  getAll: async () => {
    const { data, error } = await supabase
      .from('players')
      .select(`
        *,
        teams (
          id,
          name,
          logo_url
        )
      `)
      .order('name')

    if (error) throw error
    return data
  },

  // إضافة لاعب جديد
  create: async (player) => {
    const { data, error } = await supabase
      .from('players')
      .insert([player])
      .select()

    if (error) throw error
    return data[0]
  },

  // تحديث لاعب
  update: async (id, updates) => {
    const { data, error } = await supabase
      .from('players')
      .update(updates)
      .eq('id', id)
      .select()

    if (error) throw error
    return data[0]
  },

  // حذف لاعب
  delete: async (id) => {
    const { error } = await supabase
      .from('players')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

// ===== إدارة المباريات =====
export const matchesAPI = {
  // جلب جميع المباريات مع معلومات الفرق والبطولة
  getAll: async () => {
    const { data, error } = await supabase
      .from('matches')
      .select(`
        *,
        home_team:teams!matches_home_team_id_fkey (
          id,
          name,
          logo_url
        ),
        away_team:teams!matches_away_team_id_fkey (
          id,
          name,
          logo_url
        ),
        leagues (
          id,
          name
        )
      `)
      .order('match_date', { ascending: false })

    if (error) throw error
    return data
  },

  // إضافة مباراة جديدة
  create: async (match) => {
    const { data, error } = await supabase
      .from('matches')
      .insert([match])
      .select()

    if (error) throw error
    return data[0]
  },

  // تحديث مباراة
  update: async (id, updates) => {
    const { data, error } = await supabase
      .from('matches')
      .update(updates)
      .eq('id', id)
      .select()

    if (error) throw error
    return data[0]
  },

  // حذف مباراة
  delete: async (id) => {
    const { error } = await supabase
      .from('matches')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

// ===== إدارة الأخبار =====
export const newsAPI = {
  // جلب جميع الأخبار
  getAll: async () => {
    const { data, error } = await supabase
      .from('news')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  },

  // إضافة خبر جديد
  create: async (news) => {
    const { data, error } = await supabase
      .from('news')
      .insert([news])
      .select()

    if (error) throw error
    return data[0]
  },

  // تحديث خبر
  update: async (id, updates) => {
    const { data, error } = await supabase
      .from('news')
      .update(updates)
      .eq('id', id)
      .select()

    if (error) throw error
    return data[0]
  },

  // حذف خبر
  delete: async (id) => {
    const { error } = await supabase
      .from('news')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

// ===== إدارة جدول الترتيب =====
export const standingsAPI = {
  // جلب ترتيب بطولة معينة
  getByLeague: async (leagueId) => {
    const { data, error } = await supabase
      .from('standings')
      .select(`
        *,
        teams (
          id,
          name,
          logo_url
        )
      `)
      .eq('league_id', leagueId)
      .order('points', { ascending: false })
      .order('goal_difference', { ascending: false })

    if (error) throw error
    return data
  },

  // تحديث ترتيب فريق
  update: async (id, updates) => {
    const { data, error } = await supabase
      .from('standings')
      .update(updates)
      .eq('id', id)
      .select()

    if (error) throw error
    return data[0]
  }
}

// ===== إدارة إحصائيات المباريات =====
export const matchStatsAPI = {
  // جلب إحصائيات مباراة معينة
  getByMatch: async (matchId) => {
    const { data, error } = await supabase
      .from('match_stats')
      .select('*')
      .eq('match_id', matchId)

    if (error) throw error
    return data[0]
  },

  // إضافة أو تحديث إحصائيات مباراة
  upsert: async (stats) => {
    const { data, error } = await supabase
      .from('match_stats')
      .upsert([stats])
      .select()

    if (error) throw error
    return data[0]
  }
}

// ===== دوال مساعدة للإحصائيات =====
export const statsAPI = {
  // إحصائيات عامة للوحة التحكم
  getDashboardStats: async () => {
    const [teamsCount, playersCount, matchesToday, newsCount] = await Promise.all([
      supabase.from('teams').select('id', { count: 'exact' }),
      supabase.from('players').select('id', { count: 'exact' }),
      supabase.from('matches').select('id', { count: 'exact' }).gte('match_date', new Date().toISOString().split('T')[0]),
      supabase.from('news').select('id', { count: 'exact' })
    ])

    return {
      teams: teamsCount.count || 0,
      players: playersCount.count || 0,
      matchesToday: matchesToday.count || 0,
      news: newsCount.count || 0
    }
  }
}
