import React, { useState, useEffect } from 'react'
import { Plus, Search } from 'lucide-react'
import { toast } from 'react-hot-toast'
import Table from '../components/shared/Table'
import Modal from '../components/shared/Modal'
import { InputField, SelectField, FormButtons } from '../components/shared/Form'
import { playersAPI, teamsAPI } from '../services/supabase'

const Players = () => {
  const [players, setPlayers] = useState([])
  const [teams, setTeams] = useState([])
  const [loading, setLoading] = useState(true)
  const [modalOpen, setModalOpen] = useState(false)
  const [editingPlayer, setEditingPlayer] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [formData, setFormData] = useState({
    name: '',
    position: '',
    age: '',
    nationality: '',
    photo_url: '',
    team_id: '',
    jersey_number: '',
    market_value: ''
  })
  const [formLoading, setFormLoading] = useState(false)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const [playersData, teamsData] = await Promise.all([
        playersAPI.getAll(),
        teamsAPI.getAll()
      ])
      
      setPlayers(playersData)
      setTeams(teamsData)
    } catch (error) {
      console.error('Error loading data:', error)
      toast.error('حدث خطأ في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    try {
      setFormLoading(true)
      
      if (editingPlayer) {
        await playersAPI.update(editingPlayer.id, formData)
        toast.success('تم تحديث اللاعب بنجاح')
      } else {
        await playersAPI.create(formData)
        toast.success('تم إضافة اللاعب بنجاح')
      }
      
      await loadData()
      closeModal()
    } catch (error) {
      console.error('Error saving player:', error)
      toast.error('حدث خطأ في حفظ اللاعب')
    } finally {
      setFormLoading(false)
    }
  }

  const handleEdit = (player) => {
    setEditingPlayer(player)
    setFormData({
      name: player.name,
      position: player.position || '',
      age: player.age || '',
      nationality: player.nationality || '',
      photo_url: player.photo_url || '',
      team_id: player.team_id || '',
      jersey_number: player.jersey_number || '',
      market_value: player.market_value || ''
    })
    setModalOpen(true)
  }

  const handleDelete = async (player) => {
    if (window.confirm('هل أنت متأكد من حذف هذا اللاعب؟')) {
      try {
        await playersAPI.delete(player.id)
        toast.success('تم حذف اللاعب بنجاح')
        await loadData()
      } catch (error) {
        console.error('Error deleting player:', error)
        toast.error('حدث خطأ في حذف اللاعب')
      }
    }
  }

  const openAddModal = () => {
    setEditingPlayer(null)
    setFormData({
      name: '',
      position: '',
      age: '',
      nationality: '',
      photo_url: '',
      team_id: '',
      jersey_number: '',
      market_value: ''
    })
    setModalOpen(true)
  }

  const closeModal = () => {
    setModalOpen(false)
    setEditingPlayer(null)
  }

  const filteredPlayers = players.filter(player =>
    player.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    player.position?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    player.teams?.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const columns = [
    {
      header: 'الصورة',
      key: 'photo_url',
      render: (value) => (
        value ? (
          <img src={value} alt="صورة اللاعب" className="w-10 h-10 rounded-full object-cover" />
        ) : (
          <div className="w-10 h-10 bg-secondary-200 rounded-full flex items-center justify-center">
            <span className="text-secondary-500 text-xs">لا يوجد</span>
          </div>
        )
      )
    },
    {
      header: 'اسم اللاعب',
      key: 'name'
    },
    {
      header: 'المركز',
      key: 'position'
    },
    {
      header: 'العمر',
      key: 'age'
    },
    {
      header: 'الجنسية',
      key: 'nationality'
    },
    {
      header: 'الفريق',
      key: 'team',
      render: (value, row) => row.teams?.name || 'غير محدد'
    },
    {
      header: 'رقم القميص',
      key: 'jersey_number'
    }
  ]

  const teamOptions = teams.map(team => ({
    value: team.id,
    label: team.name
  }))

  const positionOptions = [
    { value: 'حارس مرمى', label: 'حارس مرمى' },
    { value: 'مدافع', label: 'مدافع' },
    { value: 'وسط', label: 'وسط' },
    { value: 'مهاجم', label: 'مهاجم' }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-secondary-900">إدارة اللاعبين</h1>
        <button
          onClick={openAddModal}
          className="btn-primary flex items-center"
        >
          <Plus className="h-5 w-5 ml-2" />
          إضافة لاعب
        </button>
      </div>

      {/* Search */}
      <div className="card">
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-400 h-5 w-5" />
          <input
            type="text"
            placeholder="البحث في اللاعبين..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="input-field pr-10"
          />
        </div>
      </div>

      {/* Players Table */}
      <Table
        columns={columns}
        data={filteredPlayers}
        onEdit={handleEdit}
        onDelete={handleDelete}
        loading={loading}
        emptyMessage="لا يوجد لاعبين"
      />

      {/* Add/Edit Modal */}
      <Modal
        isOpen={modalOpen}
        onClose={closeModal}
        title={editingPlayer ? 'تعديل اللاعب' : 'إضافة لاعب جديد'}
        size="lg"
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <InputField
            label="اسم اللاعب"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            placeholder="أدخل اسم اللاعب"
            required
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <SelectField
              label="المركز"
              name="position"
              value={formData.position}
              onChange={handleInputChange}
              options={positionOptions}
              placeholder="اختر المركز"
            />
            
            <InputField
              label="العمر"
              name="age"
              type="number"
              min="16"
              max="50"
              value={formData.age}
              onChange={handleInputChange}
              placeholder="مثال: 25"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <InputField
              label="الجنسية"
              name="nationality"
              value={formData.nationality}
              onChange={handleInputChange}
              placeholder="أدخل الجنسية"
            />
            
            <InputField
              label="رقم القميص"
              name="jersey_number"
              type="number"
              min="1"
              max="99"
              value={formData.jersey_number}
              onChange={handleInputChange}
              placeholder="مثال: 10"
            />
          </div>

          <SelectField
            label="الفريق"
            name="team_id"
            value={formData.team_id}
            onChange={handleInputChange}
            options={teamOptions}
            placeholder="اختر الفريق"
          />

          <InputField
            label="القيمة السوقية (بالمليون)"
            name="market_value"
            type="number"
            step="0.1"
            min="0"
            value={formData.market_value}
            onChange={handleInputChange}
            placeholder="مثال: 5.5"
          />

          <InputField
            label="رابط الصورة"
            name="photo_url"
            type="url"
            value={formData.photo_url}
            onChange={handleInputChange}
            placeholder="https://example.com/photo.jpg"
          />

          <FormButtons
            onSubmit={handleSubmit}
            onCancel={closeModal}
            loading={formLoading}
            submitText={editingPlayer ? 'تحديث' : 'إضافة'}
          />
        </form>
      </Modal>
    </div>
  )
}

export default Players
